{"name": "ccxt-funding", "version": "1.0.0", "description": "CLI tool for fetching and comparing funding rates from Bybit and Hyperliquid", "main": "dist/lib/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/cli/index.js", "dev": "tsc && node dist/cli/index.js", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "fetch-bybit": "tsx src/cli/fetch-bybit-data.ts", "analyze-delta": "tsx src/cli/analyze-delta-neutral.ts", "fetch-historical": "tsx src/cli/fetch-historical.ts", "backtest": "tsx src/cli/backtest.ts", "discover-pairs": "tsx src/cli/discover-pairs.ts", "optimize-ml": "tsx src/cli/optimize-ml.ts"}, "keywords": ["ccxt", "funding-rates", "bybit", "hyperliquid", "crypto", "cli"], "author": "", "license": "MIT", "engines": {"node": ">=16.0.0"}, "dependencies": {"@fugle/backtest": "^0.2.0", "ccxt": "^4.4.92", "chalk": "^5.4.1", "cli-table3": "^0.6.5", "commander": "^14.0.0", "dotenv": "^17.0.1", "ml-random-forest": "^2.1.0", "ml-regression": "^6.3.0", "open": "^10.1.2", "ora": "^8.2.0", "table": "^6.9.0"}, "devDependencies": {"@types/node": "^24.0.10", "tsx": "^4.20.3", "typescript": "^5.8.3"}}