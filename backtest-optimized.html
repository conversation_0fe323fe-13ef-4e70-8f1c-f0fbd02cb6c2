<!DOCTYPE html>
<html>
<head>
  <title>Comprehensive Backtest Analysis</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      color: #333;
      padding: 20px;
      line-height: 1.6;
    }
    
    .container {
      max-width: 1400px;
      margin: 0 auto;
    }
    
    h1, h2 {
      color: #2c3e50;
      margin-bottom: 20px;
    }
    
    h1 {
      text-align: center;
      margin-bottom: 40px;
      font-size: 2.5em;
    }
    
    h2 {
      margin-top: 40px;
      border-bottom: 2px solid #3498db;
      padding-bottom: 10px;
    }
    
    .summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 40px;
    }
    
    .stat-card {
      background: white;
      padding: 25px;
      border-radius: 12px;
      text-align: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    }
    
    .stat-value {
      font-size: 2.2em;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .stat-label {
      color: #7f8c8d;
      font-size: 0.9em;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
    
    .positive { color: #27ae60; }
    .negative { color: #e74c3c; }
    .neutral { color: #3498db; }
    
    .chart-container {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.08);
      margin-bottom: 40px;
    }
    
    .chart-container canvas {
      max-height: 400px;
    }
    
    .table-section {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.08);
      margin-bottom: 40px;
      overflow-x: auto;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
    }
    
    th {
      background: #34495e;
      color: white;
      padding: 12px;
      text-align: left;
      font-weight: 600;
    }
    
    td {
      padding: 10px 12px;
      border-bottom: 1px solid #ecf0f1;
    }
    
    tr:hover {
      background: #f8f9fa;
    }
    
    .detail-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 40px;
    }
    
    .detail-card {
      background: white;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    }
    
    .detail-row {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      border-bottom: 1px solid #ecf0f1;
    }
    
    .detail-row:last-child {
      border-bottom: none;
    }
    
    .info-box {
      background: #ecf0f1;
      padding: 15px;
      border-radius: 8px;
      margin: 20px 0;
    }
    
    .footer {
      text-align: center;
      margin-top: 60px;
      color: #95a5a6;
      font-size: 0.9em;
    }
    
    @media print {
      body {
        background: white;
      }
      .chart-container, .table-section, .detail-card {
        box-shadow: none;
        break-inside: avoid;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎯 Comprehensive Delta-Neutral Backtest Analysis</h1>
    
    <!-- Executive Summary -->
    <div class="summary">
      <div class="stat-card">
        <div class="stat-value positive">
          +0.9%
        </div>
        <div class="stat-label">Total Return</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-value positive">
          49.5%
        </div>
        <div class="stat-label">Annualized</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-value neutral">
          16
        </div>
        <div class="stat-label">Total Trades</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-value neutral">
          6%
        </div>
        <div class="stat-label">Win Rate</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-value negative">
          -4.1%
        </div>
        <div class="stat-label">Max Drawdown</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-value positive">
          $5
        </div>
        <div class="stat-label">Funding Collected</div>
      </div>
    </div>
    
    <!-- Performance Charts -->
    <h2>📈 Performance Analysis</h2>
    
    <div class="chart-container">
      <h3>Equity Curve</h3>
      <canvas id="equityChart"></canvas>
    </div>
    
    <div class="chart-container">
      <h3>Monthly Performance</h3>
      <canvas id="monthlyChart"></canvas>
    </div>
    
    <!-- Monthly Breakdown -->
    <h2>📅 Monthly Breakdown</h2>
    <div class="table-section">
      <table>
        <thead>
          <tr>
            <th>Month</th>
            <th>Trades</th>
            <th>Profit</th>
            <th>Return</th>
            <th>Win Rate</th>
          </tr>
        </thead>
        <tbody>
          
        </tbody>
      </table>
    </div>
    
    <!-- Symbol Performance -->
    <h2>💎 Performance by Symbol</h2>
    <div class="table-section">
      <table>
        <thead>
          <tr>
            <th>Symbol</th>
            <th>Trades</th>
            <th>Total P&L</th>
            <th>Avg P&L</th>
            <th>Win Rate</th>
            <th>Avg Entry APR</th>
            <th>Funding Collected</th>
          </tr>
        </thead>
        <tbody>
          
        <tr>
          <td>FIL/USDT:USDT</td>
          <td>1</td>
          <td class="positive">$0.11</td>
          <td class="positive">$0.11</td>
          <td>100%</td>
          <td>11.0%</td>
          <td>$1.32</td>
        </tr>
      
        <tr>
          <td>WLD/USDT:USDT</td>
          <td>1</td>
          <td class="negative">$-0.72</td>
          <td class="negative">$-0.72</td>
          <td>0%</td>
          <td>10.6%</td>
          <td>$0.12</td>
        </tr>
      
        <tr>
          <td>DOT/USDT:USDT</td>
          <td>1</td>
          <td class="negative">$-1.01</td>
          <td class="negative">$-1.01</td>
          <td>0%</td>
          <td>7.4%</td>
          <td>$0.38</td>
        </tr>
      
        <tr>
          <td>UNI/USDT:USDT</td>
          <td>1</td>
          <td class="negative">$-1.07</td>
          <td class="negative">$-1.07</td>
          <td>0%</td>
          <td>9.6%</td>
          <td>$0.19</td>
        </tr>
      
        <tr>
          <td>XRP/USDT:USDT</td>
          <td>1</td>
          <td class="negative">$-1.88</td>
          <td class="negative">$-1.88</td>
          <td>0%</td>
          <td>11.0%</td>
          <td>$0.55</td>
        </tr>
      
        <tr>
          <td>LTC/USDT:USDT</td>
          <td>1</td>
          <td class="negative">$-2.00</td>
          <td class="negative">$-2.00</td>
          <td>0%</td>
          <td>11.0%</td>
          <td>$0.18</td>
        </tr>
      
        <tr>
          <td>LDO/USDT:USDT</td>
          <td>1</td>
          <td class="negative">$-2.39</td>
          <td class="negative">$-2.39</td>
          <td>0%</td>
          <td>11.0%</td>
          <td>$0.58</td>
        </tr>
      
        <tr>
          <td>NEAR/USDT:USDT</td>
          <td>1</td>
          <td class="negative">$-3.62</td>
          <td class="negative">$-3.62</td>
          <td>0%</td>
          <td>11.0%</td>
          <td>$0.11</td>
        </tr>
      
        <tr>
          <td>LINK/USDT:USDT</td>
          <td>2</td>
          <td class="negative">$-3.92</td>
          <td class="negative">$-1.96</td>
          <td>0%</td>
          <td>9.4%</td>
          <td>$0.44</td>
        </tr>
      
        <tr>
          <td>MKR/USDT:USDT</td>
          <td>1</td>
          <td class="negative">$-3.93</td>
          <td class="negative">$-3.93</td>
          <td>0%</td>
          <td>11.0%</td>
          <td>$0.05</td>
        </tr>
      
        <tr>
          <td>CRV/USDT:USDT</td>
          <td>1</td>
          <td class="negative">$-4.12</td>
          <td class="negative">$-4.12</td>
          <td>0%</td>
          <td>9.4%</td>
          <td>$-0.13</td>
        </tr>
      
        <tr>
          <td>RENDER/USDT:USDT</td>
          <td>2</td>
          <td class="negative">$-5.40</td>
          <td class="negative">$-2.70</td>
          <td>0%</td>
          <td>5.2%</td>
          <td>$0.00</td>
        </tr>
      
        <tr>
          <td>BNB/USDT:USDT</td>
          <td>2</td>
          <td class="negative">$-5.84</td>
          <td class="negative">$-2.92</td>
          <td>0%</td>
          <td>8.9%</td>
          <td>$0.71</td>
        </tr>
      
        </tbody>
      </table>
    </div>
    
    <!-- P&L Breakdown -->
    <h2>💰 P&L Analysis</h2>
    <div class="detail-grid">
      <div class="detail-card">
        <h3>Revenue Sources</h3>
        <div class="detail-row">
          <span>Funding Collected</span>
          <span class="positive">+$4.51</span>
        </div>
        <div class="detail-row">
          <span>Spot P&L</span>
          <span>$17.34</span>
        </div>
        <div class="detail-row">
          <span>Perp P&L</span>
          <span>$-12.53</span>
        </div>
      </div>
      
      <div class="detail-card">
        <h3>Costs</h3>
        <div class="detail-row">
          <span>Trading Fees</span>
          <span class="negative">-$45.11</span>
        </div>
        <div class="detail-row">
          <span>Average Fee per Trade</span>
          <span class="negative">-$2.82</span>
        </div>
      </div>
    </div>
    
    <!-- Top Trades -->
    <h2>🏆 Best & Worst Trades</h2>
    <div class="detail-grid">
      <div class="detail-card">
        <h3>Top 5 Best Trades</h3>
        
          <div class="detail-row">
            <span>FIL/USDT:USDT (104h)</span>
            <span class="positive">+$0.11</span>
          </div>
        
          <div class="detail-row">
            <span>WLD/USDT:USDT (16h)</span>
            <span class="positive">+$-0.72</span>
          </div>
        
          <div class="detail-row">
            <span>DOT/USDT:USDT (48h)</span>
            <span class="positive">+$-1.01</span>
          </div>
        
          <div class="detail-row">
            <span>UNI/USDT:USDT (32h)</span>
            <span class="positive">+$-1.07</span>
          </div>
        
          <div class="detail-row">
            <span>LINK/USDT:USDT (48h)</span>
            <span class="positive">+$-1.40</span>
          </div>
        
      </div>
      
      <div class="detail-card">
        <h3>Top 5 Worst Trades</h3>
        
          <div class="detail-row">
            <span>CRV/USDT:USDT (8h)</span>
            <span class="negative">$-4.12</span>
          </div>
        
          <div class="detail-row">
            <span>MKR/USDT:USDT (24h)</span>
            <span class="negative">$-3.93</span>
          </div>
        
          <div class="detail-row">
            <span>RENDER/USDT:USDT (8h)</span>
            <span class="negative">$-3.69</span>
          </div>
        
          <div class="detail-row">
            <span>NEAR/USDT:USDT (16h)</span>
            <span class="negative">$-3.62</span>
          </div>
        
          <div class="detail-row">
            <span>BNB/USDT:USDT (24h)</span>
            <span class="negative">$-3.57</span>
          </div>
        
      </div>
    </div>
    
    <!-- All Trades Table -->
    <h2>📊 All Trades (16)</h2>
    <div class="table-section">
      <table id="tradesTable">
        <thead>
          <tr>
            <th>Entry Date</th>
            <th>Symbol</th>
            <th>Entry APR</th>
            <th>Exit APR</th>
            <th>Hours Held</th>
            <th>Funding Periods</th>
            <th>Funding Collected</th>
            <th>Net P&L</th>
            <th>Exit Reason</th>
          </tr>
        </thead>
        <tbody>
          
        <tr>
          <td>6/29/2025</td>
          <td>NEAR/USDT:USDT</td>
          <td>11.0%</td>
          <td>-6.3%</td>
          <td>16.0</td>
          <td>2</td>
          <td class="positive">$0.11</td>
          <td class="negative">$-3.62</td>
          <td>ML Signal: Exit soon + Low APR (-6.3%)</td>
        </tr>
      
        <tr>
          <td>6/29/2025</td>
          <td>WLD/USDT:USDT</td>
          <td>10.6%</td>
          <td>0.6%</td>
          <td>16.0</td>
          <td>2</td>
          <td class="positive">$0.12</td>
          <td class="negative">$-0.72</td>
          <td>ML Signal: Exit soon + Low APR (0.6%)</td>
        </tr>
      
        <tr>
          <td>6/29/2025</td>
          <td>MKR/USDT:USDT</td>
          <td>11.0%</td>
          <td>-11.4%</td>
          <td>24.0</td>
          <td>3</td>
          <td class="positive">$0.05</td>
          <td class="negative">$-3.93</td>
          <td>ML Signal: Exit soon + Low APR (-11.4%)</td>
        </tr>
      
        <tr>
          <td>6/29/2025</td>
          <td>UNI/USDT:USDT</td>
          <td>9.6%</td>
          <td>-3.7%</td>
          <td>32.0</td>
          <td>4</td>
          <td class="positive">$0.19</td>
          <td class="negative">$-1.07</td>
          <td>ML Signal: Exit soon + Low APR (-3.7%)</td>
        </tr>
      
        <tr>
          <td>6/29/2025</td>
          <td>LDO/USDT:USDT</td>
          <td>11.0%</td>
          <td>0.2%</td>
          <td>40.0</td>
          <td>5</td>
          <td class="positive">$0.58</td>
          <td class="negative">$-2.39</td>
          <td>ML Signal: Exit soon + Low APR (0.2%)</td>
        </tr>
      
        <tr>
          <td>6/30/2025</td>
          <td>BNB/USDT:USDT</td>
          <td>11.0%</td>
          <td>-0.3%</td>
          <td>24.0</td>
          <td>3</td>
          <td class="positive">$0.30</td>
          <td class="negative">$-2.27</td>
          <td>ML Signal: Exit soon + Low APR (-0.3%)</td>
        </tr>
      
        <tr>
          <td>7/1/2025</td>
          <td>LINK/USDT:USDT</td>
          <td>11.0%</td>
          <td>-5.4%</td>
          <td>8.0</td>
          <td>1</td>
          <td class="negative">$-0.06</td>
          <td class="negative">$-2.52</td>
          <td>ML Signal: Exit soon + Low APR (-5.4%)</td>
        </tr>
      
        <tr>
          <td>6/30/2025</td>
          <td>LTC/USDT:USDT</td>
          <td>11.0%</td>
          <td>-9.0%</td>
          <td>32.0</td>
          <td>4</td>
          <td class="positive">$0.18</td>
          <td class="negative">$-2.00</td>
          <td>ML Signal: Exit soon + Low APR (-9.0%)</td>
        </tr>
      
        <tr>
          <td>7/2/2025</td>
          <td>BNB/USDT:USDT</td>
          <td>6.8%</td>
          <td>-1.3%</td>
          <td>24.0</td>
          <td>3</td>
          <td class="positive">$0.41</td>
          <td class="negative">$-3.57</td>
          <td>ML Signal: Exit soon + Low APR (-1.3%)</td>
        </tr>
      
        <tr>
          <td>7/2/2025</td>
          <td>LINK/USDT:USDT</td>
          <td>7.8%</td>
          <td>1.2%</td>
          <td>48.0</td>
          <td>6</td>
          <td class="positive">$0.50</td>
          <td class="negative">$-1.40</td>
          <td>ML Signal: Exit soon + Low APR (1.2%)</td>
        </tr>
      
        <tr>
          <td>7/2/2025</td>
          <td>DOT/USDT:USDT</td>
          <td>7.4%</td>
          <td>-3.5%</td>
          <td>48.0</td>
          <td>6</td>
          <td class="positive">$0.38</td>
          <td class="negative">$-1.01</td>
          <td>ML Signal: Exit now (risk: 36.1%, momentum: declining)</td>
        </tr>
      
        <tr>
          <td>6/30/2025</td>
          <td>FIL/USDT:USDT</td>
          <td>11.0%</td>
          <td>2.0%</td>
          <td>104.0</td>
          <td>13</td>
          <td class="positive">$1.32</td>
          <td class="positive">$0.11</td>
          <td>ML Signal: Exit soon + Low APR (2.0%)</td>
        </tr>
      
        <tr>
          <td>7/3/2025</td>
          <td>XRP/USDT:USDT</td>
          <td>11.0%</td>
          <td>-2.1%</td>
          <td>48.0</td>
          <td>6</td>
          <td class="positive">$0.55</td>
          <td class="negative">$-1.88</td>
          <td>ML Signal: Exit soon + Low APR (-2.1%)</td>
        </tr>
      
        <tr>
          <td>7/5/2025</td>
          <td>CRV/USDT:USDT</td>
          <td>9.4%</td>
          <td>-7.2%</td>
          <td>8.0</td>
          <td>1</td>
          <td class="negative">$-0.13</td>
          <td class="negative">$-4.12</td>
          <td>ML Signal: Exit soon + Low APR (-7.2%)</td>
        </tr>
      
        <tr>
          <td>7/5/2025</td>
          <td>RENDER/USDT:USDT</td>
          <td>5.5%</td>
          <td>-0.7%</td>
          <td>8.0</td>
          <td>1</td>
          <td class="negative">$-0.01</td>
          <td class="negative">$-3.69</td>
          <td>ML Signal: Exit soon + Low APR (-0.7%)</td>
        </tr>
      
        <tr>
          <td>7/5/2025</td>
          <td>RENDER/USDT:USDT</td>
          <td>5.0%</td>
          <td>1.9%</td>
          <td>8.0</td>
          <td>1</td>
          <td class="positive">$0.01</td>
          <td class="negative">$-1.72</td>
          <td>ML Signal: Exit soon + Low APR (1.9%)</td>
        </tr>
      
        </tbody>
      </table>
    </div>
    
    <!-- Strategy Information -->
    <div class="info-box">
      <h3>Strategy Configuration</h3>
      <p><strong>Initial Capital:</strong> $10,000</p>
      <p><strong>Minimum Funding APR:</strong> 3%</p>
      <p><strong>Backtest Period:</strong> 7 days</p>
      <p><strong>Start Date:</strong> 6/29/2025</p>
      <p><strong>End Date:</strong> 7/6/2025</p>
    </div>
    
    <div class="footer">
      <p>Generated on 7/6/2025, 3:01:20 AM</p>
      <p>Delta-Neutral Funding Arbitrage Strategy | Comprehensive Backtest Report</p>
    </div>
  </div>
  
  <script>
    // Equity Chart
    const ctx1 = document.getElementById('equityChart').getContext('2d');
    const gradient = ctx1.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, 'rgba(46, 204, 113, 0.2)');
    gradient.addColorStop(1, 'rgba(46, 204, 113, 0.01)');
    
    new Chart(ctx1, {
      type: 'line',
      data: {
        labels: ["6/29","6/29","6/30","6/30","6/30","7/1","7/1","7/1","7/2","7/2","7/2","7/3","7/3","7/3","7/4","7/4","7/4","7/5","7/5","7/5","7/6"],
        datasets: [{
          label: 'Portfolio Value ($)',
          data: ["10000.00","10000.00","10352.17","10120.19","10089.72","10081.43","9994.46","9961.15","9928.45","10034.72","10278.53","10226.28","10319.88","10246.23","10267.34","10144.11","10081.79","10098.69","10064.04","10077.82","10094.98"],
          borderColor: '#2ecc71',
          backgroundColor: gradient,
          borderWidth: 3,
          fill: true,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 6,
          pointBackgroundColor: '#2ecc71'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: true,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            padding: 12,
            cornerRadius: 8,
            callbacks: {
              label: function(context) {
                return '$' + context.formattedValue;
              }
            }
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            },
            ticks: {
              maxRotation: 0,
              autoSkip: true,
              maxTicksLimit: 10
            }
          },
          y: {
            beginAtZero: false,
            grid: {
              color: '#f0f0f0'
            },
            ticks: {
              callback: function(value) {
                return '$' + value.toLocaleString();
              }
            }
          }
        }
      }
    });
    
    // Monthly Chart
    const ctx2 = document.getElementById('monthlyChart').getContext('2d');
    new Chart(ctx2, {
      type: 'bar',
      data: {
        labels: [],
        datasets: [{
          label: 'Monthly Profit ($)',
          data: [],
          backgroundColor: function(context) {
            const value = context.parsed.y;
            return value >= 0 ? 'rgba(46, 204, 113, 0.8)' : 'rgba(231, 76, 60, 0.8)';
          },
          borderColor: function(context) {
            const value = context.parsed.y;
            return value >= 0 ? '#27ae60' : '#e74c3c';
          },
          borderWidth: 2
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return '$' + context.formattedValue;
              }
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: '#f0f0f0'
            },
            ticks: {
              callback: function(value) {
                return '$' + value.toLocaleString();
              }
            }
          }
        }
      }
    });
    
    // Initialize DataTable
    $(document).ready(function() {
      $('#tradesTable').DataTable({
        pageLength: 25,
        order: [[0, 'desc']],
        columnDefs: [
          { targets: [2, 3, 4, 5, 6, 7], className: 'dt-right' }
        ]
      });
    });
  </script>
</body>
</html>