# Funding Rate CLI Configuration
# Copy this file to .env and fill in your API credentials

# Network Selection (default: testnet for safety)
# DEFAULT_NETWORK=testnet

# ========== TESTNET CONFIGURATION ==========
# Bybit Testnet API Configuration
# Get your testnet API keys from: https://testnet.bybit.com/
BYBIT_TESTNET_API_KEY=your_bybit_testnet_api_key_here
BYBIT_TESTNET_API_SECRET=your_bybit_testnet_api_secret_here

# Hyperliquid Testnet API Configuration
# Get your testnet API keys from: https://app.hyperliquid-testnet.xyz/
HYPERLIQUID_TESTNET_API_KEY=your_hyperliquid_testnet_api_key_here
HYPERLIQUID_TESTNET_API_SECRET=your_hyperliquid_testnet_api_secret_here

# ========== MAINNET CONFIGURATION ==========
# WARNING: These are REAL MONEY credentials. Be careful!

# Bybit Mainnet API Configuration
# Get your mainnet API keys from: https://www.bybit.com/
# Used by: funding rate CLI and fetch-bybit data tool
BYBIT_MAINNET_API_KEY=your_bybit_mainnet_api_key_here
BYBIT_MAINNET_API_SECRET=your_bybit_mainnet_api_secret_here

# Hyperliquid Mainnet API Configuration
# Get your mainnet API keys from: https://app.hyperliquid.xyz/
HYPERLIQUID_MAINNET_API_KEY=your_hyperliquid_mainnet_api_key_here
HYPERLIQUID_MAINNET_API_SECRET=your_hyperliquid_mainnet_api_secret_here

# ========== SYMBOL CONFIGURATION ==========
# Exchange-specific symbols (perpetual contracts)
# These work for both testnet and mainnet

# Bybit uses USDT settlement
BYBIT_SYMBOLS=BTC/USDT:USDT,ETH/USDT:USDT,SOL/USDT:USDT

# Hyperliquid uses USDC settlement
HYPERLIQUID_SYMBOLS=BTC/USDC:USDC,ETH/USDC:USDC,SOL/USDC:USDC

# ========== OPTIONAL CONFIGURATION ==========
# Optional: Default output format (table, json, csv)
OUTPUT_FORMAT=table

# Optional: Enable debug logging (true/false)
DEBUG=false
