# Environment variables
.env
.env.local
.env.*.local
.env.production
.env.development
.env.test

# Dependencies
node_modules/
.pnpm-store/

# Build outputs
dist/
build/
out/
*.tsbuildinfo
.next/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE files
.vscode/
!.vscode/extensions.json
!.vscode/settings.json.example
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.sublime-project

# Testing
coverage/
.nyc_output/
test-results/
junit.xml

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.cache
.cache/

# Package files
*.tgz
*.tar.gz
*.zip

# Security - API keys and secrets
*.key
*.pem
*.p12
*.pfx
credentials.json
secrets.json
config.local.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
ENV/
env/

# Backup files
*.bak
*.backup
*~
*.orig

# Lock files (except pnpm)
package-lock.json
yarn.lock

# Other
.sass-cache/
.parcel-cache/
.turbo/

# Data directory
data/
data_backup

# Generated backtest results
backtest-results.html
backtest-results.json

# ROFL and containerization
rofl/data/
rofl/logs/
rofl/runtime/
docker/volumes/
*.pid
.rofl/
rofl-*.log
container-*.log

# Podman/Docker runtime
.podman/
.docker/
docker-compose.override.yml
compose.override.yml
