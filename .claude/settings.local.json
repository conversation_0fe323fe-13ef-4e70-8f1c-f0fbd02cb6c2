{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(pnpm init:*)", "Bash(pnpm add:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(pnpm tsc:*)", "Bash(pnpm start:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(pnpm build:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(pnpm list:*)", "Bash(pnpm fetch-bybit:*)", "Bash(ls:*)", "Bash(grep:*)", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(cat:*)", "Bash(pnpm analyze-delta:*)", "<PERSON><PERSON>(python3:*)", "Bash(find:*)", "Bash(rg:*)", "Bash(pnpm fetch-historical:*)", "Bash(pnpm backtest:*)", "Bash(git branch:*)", "<PERSON><PERSON>(git rev-list:*)", "Bash(git filter-branch:*)", "Bash(git reset --hard backup-original-history)", "Bash(git checkout:*)", "Bash(git-filter-repo:*)", "Bash(pnpm discover-pairs:*)", "Bash(git merge:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./test-local.sh:*)", "Bash(docker-compose build:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(git worktree:*)", "Bash(cp:*)", "Bash(pnpm install:*)", "mcp__tavily__tavily-search", "Bash(npm view:*)", "<PERSON><PERSON>(diff:*)", "Bash(git rm:*)"], "deny": []}}